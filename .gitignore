# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnpm-store

# Build outputs
dist
dist-ssr
build
out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Cache directories
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Deployment
.vercel
.netlify
.firebase

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
