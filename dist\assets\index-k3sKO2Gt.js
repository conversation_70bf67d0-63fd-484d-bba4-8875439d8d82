var t=Object.defineProperty,e=(e,i,s)=>((e,i,s)=>i in e?t(e,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[i]=s)(e,"symbol"!=typeof i?i+"":i,s);import{S as i,F as s,P as o,W as n,a,A as r,D as h,b as c,B as l,c as m,d,e as p,f as u,g as y,h as g,M as f,i as w,C as x,V as M,G as v,j as S,k as E,l as b,m as k,R as C,n as T,o as A,p as L}from"./three-DKRBiI99.js";!function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const t of document.querySelectorAll('link[rel="modulepreload"]'))e(t);new MutationObserver((t=>{for(const i of t)if("childList"===i.type)for(const t of i.addedNodes)"LINK"===t.tagName&&"modulepreload"===t.rel&&e(t)})).observe(document,{childList:!0,subtree:!0})}function e(t){if(t.ep)return;t.ep=!0;const e=function(t){const e={};return t.integrity&&(e.integrity=t.integrity),t.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),"use-credentials"===t.crossOrigin?e.credentials="include":"anonymous"===t.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}(t);fetch(t.href,e)}}();class P{constructor(t){e(this,"scene"),e(this,"camera"),e(this,"renderer"),e(this,"canvas"),this.canvas=t,this.initScene(),this.initCamera(),this.initRenderer(),this.initLighting(),this.createStarField()}initScene(){this.scene=new i,this.scene.fog=new s(0,1e3,1e4)}initCamera(){this.camera=new o(75,window.innerWidth/window.innerHeight,.1,1e4),this.camera.position.set(0,50,100)}initRenderer(){this.renderer=new n({canvas:this.canvas,antialias:!0,alpha:!0}),this.renderer.setSize(window.innerWidth,window.innerHeight),this.renderer.setPixelRatio(window.devicePixelRatio),this.renderer.shadowMap.enabled=!0,this.renderer.shadowMap.type=a,this.renderer.setClearColor(17,1)}initLighting(){const t=new r(4210816,.4);this.scene.add(t);const e=new h(16777215,1.2);e.position.set(200,200,100),e.castShadow=!0,e.shadow.mapSize.width=4096,e.shadow.mapSize.height=4096,e.shadow.camera.near=.5,e.shadow.camera.far=1e3,e.shadow.camera.left=-200,e.shadow.camera.right=200,e.shadow.camera.top=200,e.shadow.camera.bottom=-200,this.scene.add(e);const i=new c(35071,.8,500);i.position.set(-100,50,-50),this.scene.add(i);const s=new c(16729088,.6,300);s.position.set(100,-50,50),this.scene.add(s);const o=new c(65450,.5,200);o.name="dynamicLight",this.scene.add(o)}createStarField(){this.createStarLayer(15e3,1.5,16777215,"nearStars"),this.createStarLayer(8e3,1,11193599,"midStars"),this.createStarLayer(5e3,.5,8952319,"farStars"),this.createNebula()}createStarLayer(t,e,i,s){const o=new l,n=new Float32Array(3*t),a=new Float32Array(3*t);for(let c=0;c<3*t;c+=3){n[c]=3e4*(Math.random()-.5),n[c+1]=3e4*(Math.random()-.5),n[c+2]=3e4*(Math.random()-.5);const t=.3,e=(i>>16&255)/255,s=(i>>8&255)/255,o=(255&i)/255;a[c]=Math.min(1,e+(Math.random()-.5)*t),a[c+1]=Math.min(1,s+(Math.random()-.5)*t),a[c+2]=Math.min(1,o+(Math.random()-.5)*t)}o.setAttribute("position",new m(n,3)),o.setAttribute("color",new m(a,3));const r=new d({size:e,sizeAttenuation:!1,vertexColors:!0,transparent:!0,opacity:.8}),h=new p(o,r);h.name=s,this.scene.add(h)}createNebula(){const t=new l,e=new Float32Array(6e3),i=new Float32Array(6e3);for(let n=0;n<6e3;n+=3)e[n]=25e3*(Math.random()-.5),e[n+1]=25e3*(Math.random()-.5),e[n+2]=25e3*(Math.random()-.5),i[n]=.5+.5*Math.random(),i[n+1]=.2+.3*Math.random(),i[n+2]=.8+.2*Math.random();t.setAttribute("position",new m(e,3)),t.setAttribute("color",new m(i,3));const s=new d({size:8,sizeAttenuation:!0,vertexColors:!0,transparent:!0,opacity:.3,blending:u}),o=new p(t,s);o.name="nebula",this.scene.add(o)}onWindowResize(){this.camera.aspect=window.innerWidth/window.innerHeight,this.camera.updateProjectionMatrix(),this.renderer.setSize(window.innerWidth,window.innerHeight)}render(){this.renderer.render(this.scene,this.camera)}addToScene(t){this.scene.add(t)}removeFromScene(t){this.scene.remove(t)}}class z{constructor(t,i,s=!0){e(this,"mesh"),e(this,"position"),e(this,"velocity"),e(this,"damage"),e(this,"isPlayerProjectile"),e(this,"boundingBox"),this.position=t.clone(),this.velocity=i.clone(),this.damage=s?25:10,this.isPlayerProjectile=s,this.boundingBox=new y,this.createMesh()}createMesh(){const t=new g(.3,8,8),e=this.isPlayerProjectile?65280:16711680,i=new f({color:e});this.mesh=new w(t,i),this.mesh.position.copy(this.position);const s=new g(.6,8,8),o=new f({color:e,transparent:!0,opacity:.3}),n=new w(s,o);this.mesh.add(n),this.createTrail()}createTrail(){const t=new x(.1,.3,2,8),e=this.isPlayerProjectile?65280:16711680,i=new f({color:e,transparent:!0,opacity:.6}),s=new w(t,i);s.rotation.x=Math.PI/2,s.position.z=1,this.mesh.add(s)}update(t){this.position.add(this.velocity.clone().multiplyScalar(t)),this.mesh.position.copy(this.position),this.boundingBox.setFromObject(this.mesh),this.mesh.rotation.x+=.1,this.mesh.rotation.y+=.1}isOutOfBounds(){return Math.abs(this.position.x)>200||Math.abs(this.position.y)>200||Math.abs(this.position.z)>1e3}checkCollision(t){const e=(new y).setFromObject(t);return this.boundingBox.intersectsBox(e)}destroy(){this.createExplosion()}createExplosion(){for(let t=0;t<10;t++){const t=new g(.1,4,4),e=new f({color:this.isPlayerProjectile?65280:16711680,transparent:!0,opacity:.8}),i=new w(t,e);i.position.copy(this.position);const s=new M(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize();i.userData={velocity:s.multiplyScalar(10*Math.random()+5),life:1}}}getBoundingBox(){return this.boundingBox}}class B{constructor(){e(this,"audioContext",null),e(this,"isPlaying",!1),e(this,"masterGain",null),e(this,"volume",.3),this.initializeAudioContext()}initializeAudioContext(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.masterGain=this.audioContext.createGain(),this.masterGain.connect(this.audioContext.destination),this.masterGain.gain.setValueAtTime(this.volume,this.audioContext.currentTime),console.log("🎵 太空音乐生成器初始化完成")}catch(t){console.warn("音频上下文初始化失败:",t)}}createOscillator(t,e="sine"){if(!this.audioContext)throw new Error("音频上下文未初始化");const i=this.audioContext.createOscillator();return i.frequency.setValueAtTime(t,this.audioContext.currentTime),i.type=e,i}createGainNode(t=0){if(!this.audioContext)throw new Error("音频上下文未初始化");const e=this.audioContext.createGain();return e.gain.setValueAtTime(t,this.audioContext.currentTime),e}createAmbientDrone(){if(!this.audioContext||!this.masterGain)return;const t=this.createOscillator(55,"sine"),e=this.createOscillator(82.4,"sine"),i=this.createOscillator(110,"triangle"),s=this.createGainNode(0);t.connect(s),e.connect(s),i.connect(s),s.connect(this.masterGain);const o=this.audioContext.currentTime;s.gain.linearRampToValueAtTime(.15,o+3),t.start(o),e.start(o),i.start(o);const n=this.createOscillator(.1,"sine"),a=this.createGainNode(2);n.connect(a),a.connect(t.frequency),n.start(o)}createSpaceMelody(){if(!this.audioContext||!this.masterGain)return;const t=[{freq:220,duration:2},{freq:246.94,duration:1.5},{freq:277.18,duration:2},{freq:329.63,duration:1},{freq:369.99,duration:2.5},{freq:329.63,duration:1},{freq:277.18,duration:2},{freq:246.94,duration:1.5}];let e=this.audioContext.currentTime+2;const i=()=>{if(!this.isPlaying||!this.audioContext||!this.masterGain)return;t.forEach(((t,i)=>{const s=this.createOscillator(t.freq,"triangle"),o=this.createGainNode(0),n=this.audioContext.createBiquadFilter();n.type="lowpass",n.frequency.setValueAtTime(800,e+.8*i),n.Q.setValueAtTime(5,e+.8*i),s.connect(n),n.connect(o),o.connect(this.masterGain);const a=e+.8*i,r=a+t.duration;o.gain.setValueAtTime(0,a),o.gain.linearRampToValueAtTime(.1,a+.1),o.gain.exponentialRampToValueAtTime(.05,r-.2),o.gain.linearRampToValueAtTime(0,r),s.start(a),s.stop(r)}));const s=t.reduce(((t,e)=>t+.8*e.duration),0);e+=s,setTimeout(i,1e3*s)};i()}createSpacePercussion(){if(!this.audioContext||!this.masterGain)return;const t=t=>{const e=this.createOscillator(60,"sine"),i=this.createGainNode(0);e.connect(i),i.connect(this.masterGain),i.gain.setValueAtTime(.3,t),i.gain.exponentialRampToValueAtTime(.01,t+.3),e.frequency.setValueAtTime(60,t),e.frequency.exponentialRampToValueAtTime(30,t+.3),e.start(t),e.stop(t+.3)},e=t=>{const e=this.audioContext.createBufferSource(),i=this.audioContext.createBuffer(1,.1*this.audioContext.sampleRate,this.audioContext.sampleRate),s=i.getChannelData(0);for(let a=0;a<s.length;a++)s[a]=2*Math.random()-1;e.buffer=i;const o=this.audioContext.createBiquadFilter();o.type="highpass",o.frequency.setValueAtTime(8e3,t);const n=this.createGainNode(0);e.connect(o),o.connect(n),n.connect(this.masterGain),n.gain.setValueAtTime(.1,t),n.gain.exponentialRampToValueAtTime(.01,t+.1),e.start(t)},i=()=>{if(!this.isPlaying||!this.audioContext)return;const s=this.audioContext.currentTime,o=.5;t(s),t(s+1),e(s+o),e(s+1.5),setTimeout(i,2e3)};setTimeout(i,4e3)}async startMusic(){var t;this.audioContext||this.initializeAudioContext(),"suspended"===(null==(t=this.audioContext)?void 0:t.state)&&await this.audioContext.resume(),this.isPlaying||(this.isPlaying=!0,console.log("🎵 开始播放太空战斗音乐"),this.createAmbientDrone(),this.createSpaceMelody(),this.createSpacePercussion())}stopMusic(){if(this.isPlaying=!1,this.audioContext&&this.masterGain){const t=this.audioContext.currentTime;this.masterGain.gain.linearRampToValueAtTime(0,t+1),setTimeout((()=>{this.masterGain&&this.masterGain.gain.setValueAtTime(this.volume,this.audioContext.currentTime)}),1e3)}console.log("🎵 太空音乐已停止")}setVolume(t){this.volume=Math.max(0,Math.min(1,t)),this.masterGain&&this.audioContext&&this.masterGain.gain.setValueAtTime(this.volume,this.audioContext.currentTime)}getIsPlaying(){return this.isPlaying}destroy(){this.stopMusic(),this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.masterGain=null}}class F{constructor(){e(this,"spaceMusicGenerator"),e(this,"soundEffects",new Map),e(this,"audioContext",null),e(this,"isMuted",!1),e(this,"bgmVolume",.3),e(this,"sfxVolume",.5),e(this,"isInitialized",!1),this.spaceMusicGenerator=new B,this.initializeAudio()}async initializeAudio(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext),await this.loadSoundEffects(),this.isInitialized=!0,console.log("🎵 音频系统初始化完成")}catch(t){console.warn("音频初始化失败:",t),this.isInitialized=!1}}async loadSoundEffects(){if(this.audioContext)try{const t=this.createShootSound();this.soundEffects.set("shoot",t);const e=this.createExplosionSound();this.soundEffects.set("explosion",e);const i=this.createHitSound();this.soundEffects.set("hit",i),console.log("🎵 音效加载完成")}catch(t){console.warn("音效生成失败:",t)}}createShootSound(){if(!this.audioContext)throw new Error("音频上下文未初始化");const t=this.audioContext.sampleRate,e=this.audioContext.createBuffer(1,.2*t,t),i=e.getChannelData(0);for(let s=0;s<i.length;s++){const e=s/t,o=800*Math.exp(10*-e),n=.3*(Math.random()-.5),a=Math.exp(8*-e);i[s]=(Math.sin(2*Math.PI*o*e)+n)*a*.3}return e}createExplosionSound(){if(!this.audioContext)throw new Error("音频上下文未初始化");const t=this.audioContext.sampleRate,e=this.audioContext.createBuffer(1,1*t,t),i=e.getChannelData(0);for(let s=0;s<i.length;s++){const e=s/t,o=2*(Math.random()-.5),n=Math.sin(2*Math.PI*60*e)*Math.exp(3*-e),a=Math.exp(2*-e);i[s]=(.7*o+.3*n)*a*.5}return e}createHitSound(){if(!this.audioContext)throw new Error("音频上下文未初始化");const t=this.audioContext.sampleRate,e=this.audioContext.createBuffer(1,.3*t,t),i=e.getChannelData(0);for(let s=0;s<i.length;s++){const e=s/t,o=1200*Math.exp(5*-e),n=.5*(Math.random()-.5),a=Math.exp(6*-e);i[s]=(Math.sin(2*Math.PI*o*e)+n)*a*.4}return e}async playBGM(){if(this.isInitialized&&!this.isMuted)try{this.spaceMusicGenerator.setVolume(this.bgmVolume),await this.spaceMusicGenerator.startMusic(),console.log("🎵 太空战斗BGM开始播放")}catch(t){console.warn("BGM播放失败:",t),this.setupUserInteractionForAudio()}}stopBGM(){this.spaceMusicGenerator.stopMusic(),console.log("🎵 BGM已停止")}playSoundEffect(t){if(!this.isInitialized||this.isMuted||!this.audioContext)return;const e=this.soundEffects.get(t);if(e)try{const t=this.audioContext.createBufferSource(),i=this.audioContext.createGain();t.buffer=e,t.connect(i),i.connect(this.audioContext.destination),i.gain.setValueAtTime(this.sfxVolume,this.audioContext.currentTime),t.start(0)}catch(i){console.warn(`音效 ${t} 播放错误:`,i)}}setupUserInteractionForAudio(){const t=async()=>{await this.playBGM(),document.removeEventListener("click",t),document.removeEventListener("keydown",t)};document.addEventListener("click",t,{once:!0}),document.addEventListener("keydown",t,{once:!0}),console.log("🎵 等待用户交互以启用音频...")}async toggleMute(){return this.isMuted=!this.isMuted,this.isMuted?this.stopBGM():await this.playBGM(),this.isMuted}setBGMVolume(t){this.bgmVolume=Math.max(0,Math.min(1,t)),this.spaceMusicGenerator.setVolume(this.bgmVolume)}setSFXVolume(t){this.sfxVolume=Math.max(0,Math.min(1,t))}isMutedState(){return this.isMuted}isBGMPlaying(){return this.spaceMusicGenerator.getIsPlaying()}destroy(){this.stopBGM(),this.spaceMusicGenerator.destroy(),this.soundEffects.clear(),this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.isInitialized=!1}}class D{constructor(){e(this,"mesh"),e(this,"position"),e(this,"velocity"),e(this,"health"),e(this,"maxHealth"),e(this,"speed"),e(this,"projectiles"),e(this,"lastShotTime"),e(this,"shotCooldown"),e(this,"weaponType"),e(this,"energy"),e(this,"maxEnergy"),e(this,"skills"),e(this,"animationTime"),e(this,"audioManager",null),this.position=new M(0,0,0),this.velocity=new M(0,0,0),this.health=100,this.maxHealth=100,this.speed=8,this.energy=100,this.maxEnergy=100,this.projectiles=[],this.lastShotTime=0,this.shotCooldown=150,this.weaponType="normal",this.animationTime=0,this.initSkills(),this.createMesh()}createMesh(){this.mesh=new v;const t=new x(.8,1.5,10,12),e=new S({color:2201331,shininess:100,specular:5227511}),i=new w(t,e);i.rotation.x=Math.PI/2,i.castShadow=!0,i.receiveShadow=!0,this.mesh.add(i);const s=new g(1.2,12,8),o=new S({color:1668818,transparent:!0,opacity:.8,shininess:150}),n=new w(s,o);n.position.set(0,.5,2),n.scale.set(1,.6,1.2),n.castShadow=!0,this.mesh.add(n);const a=new E(12,.3,3),r=new S({color:1402304,shininess:80}),h=new w(a,r);h.position.set(0,0,-1),h.castShadow=!0,this.mesh.add(h);const c=new E(6,.2,1.5),l=new w(c,r);l.position.set(0,0,-3),l.castShadow=!0,this.mesh.add(l);const m=new E(1,.3,.8),d=new S({color:4342338}),p=new w(m,d);p.position.set(-4,-.2,0),this.mesh.add(p);const u=new w(m,d);u.position.set(4,-.2,0),this.mesh.add(u);const y=new x(.6,.8,2,8),f=new S({color:3622735,shininess:120}),M=new w(y,f);M.position.set(-2.5,0,-4.5),M.rotation.x=Math.PI/2,M.castShadow=!0,this.mesh.add(M);const b=new w(y,f);b.position.set(2.5,0,-4.5),b.rotation.x=Math.PI/2,b.castShadow=!0,this.mesh.add(b),this.createEngineFlames(),this.createLights(),this.mesh.position.copy(this.position)}createEngineFlames(){const t=new b(.4,3,8),e=new f({color:16729088,transparent:!0,opacity:.8}),i=new w(t,e);i.position.set(-2.5,0,-7),i.rotation.x=Math.PI/2,i.name="leftFlame",this.mesh.add(i);const s=new w(t,e);s.position.set(2.5,0,-7),s.rotation.x=Math.PI/2,s.name="rightFlame",this.mesh.add(s);const o=new b(.2,2,6),n=new f({color:43775,transparent:!0,opacity:.9}),a=new w(o,n);a.position.set(-2.5,0,-6.5),a.rotation.x=Math.PI/2,a.name="leftInnerFlame",this.mesh.add(a);const r=new w(o,n);r.position.set(2.5,0,-6.5),r.rotation.x=Math.PI/2,r.name="rightInnerFlame",this.mesh.add(r)}createLights(){const t=new g(.1,6,6),e=new f({color:16711680}),i=new w(t,e);i.position.set(-6,0,-1),this.mesh.add(i);const s=new f({color:65280}),o=new w(t,s);o.position.set(6,0,-1),this.mesh.add(o);const n=new g(.15,8,8),a=new f({color:16777215}),r=new w(n,a);r.position.set(0,1,2),r.name="strobeLight",this.mesh.add(r)}initSkills(){this.skills={rapidFire:{name:"快速射击",cooldown:5e3,lastUsed:0,energy:30,maxEnergy:30},laserBeam:{name:"激光束",cooldown:8e3,lastUsed:0,energy:50,maxEnergy:50},missile:{name:"导弹",cooldown:1e4,lastUsed:0,energy:40,maxEnergy:40},shield:{name:"护盾",cooldown:15e3,lastUsed:0,energy:60,maxEnergy:60}}}update(t){this.animationTime+=t,this.position.add(this.velocity.clone().multiplyScalar(t)),this.mesh.position.copy(this.position),this.position.x=Math.max(-120,Math.min(120,this.position.x)),this.position.y=Math.max(-60,Math.min(60,this.position.y)),this.position.z=Math.max(-150,Math.min(150,this.position.z)),this.mesh.rotation.z=.1*-this.velocity.x,this.mesh.rotation.x=.05*this.velocity.y,this.updateEngineFlames(),this.updateStrobeLight(),this.projectiles.forEach(((e,i)=>{e.update(t),e.position.z<-1e3&&this.projectiles.splice(i,1)})),this.energy=Math.min(this.maxEnergy,this.energy+10*t),this.velocity.multiplyScalar(.92)}updateEngineFlames(){const t=this.mesh.getObjectByName("leftFlame"),e=this.mesh.getObjectByName("rightFlame"),i=this.mesh.getObjectByName("leftInnerFlame"),s=this.mesh.getObjectByName("rightInnerFlame");if(t&&e&&i&&s){const o=1+.1*this.velocity.length();t.scale.y=o+.2*Math.sin(10*this.animationTime),e.scale.y=o+.2*Math.sin(10*this.animationTime+1),i.scale.y=o+.3*Math.sin(15*this.animationTime),s.scale.y=o+.3*Math.sin(15*this.animationTime+.5)}}updateStrobeLight(){const t=this.mesh.getObjectByName("strobeLight");if(t){const e=t.material,i=.5+.3*Math.sin(8*this.animationTime);e.opacity=i}}moveLeft(){this.velocity.x=Math.max(this.velocity.x-.3*this.speed,1.5*-this.speed)}moveRight(){this.velocity.x=Math.min(this.velocity.x+.3*this.speed,1.5*this.speed)}moveUp(){this.velocity.y=Math.min(this.velocity.y+.3*this.speed,1.2*this.speed)}moveDown(){this.velocity.y=Math.max(this.velocity.y-.3*this.speed,1.2*-this.speed)}moveForward(){this.velocity.z=Math.max(this.velocity.z-.3*this.speed,1.5*-this.speed)}moveBackward(){this.velocity.z=Math.min(this.velocity.z+.3*this.speed,1.2*this.speed)}shoot(t){const e=Date.now();if(e-this.lastShotTime<this.shotCooldown)return!1;this.lastShotTime=e,this.audioManager&&this.audioManager.playSoundEffect("shoot");let i=new M(0,0,-50);if(t&&(i=t.clone().multiplyScalar(50)),"rapid"===this.weaponType)for(let s=0;s<4;s++){const t=1.5*(s-1.5),e=new z(new M(this.position.x+t,this.position.y,this.position.z),i.clone(),!0);this.projectiles.push(e)}else{const t=new z(new M(this.position.x-2,this.position.y,this.position.z),i.clone(),!0),e=new z(new M(this.position.x+2,this.position.y,this.position.z),i.clone(),!0);this.projectiles.push(t,e)}return!0}takeDamage(t){this.health=Math.max(0,this.health-t)}heal(t){this.health=Math.min(this.maxHealth,this.health+t)}isAlive(){return this.health>0}getProjectiles(){return this.projectiles}removeProjectile(t){const e=this.projectiles.indexOf(t);e>-1&&this.projectiles.splice(e,1)}useSkill(t){const e=this.skills[t];if(!e)return!1;const i=Date.now();if(i-e.lastUsed<e.cooldown||this.energy<e.energy)return!1;switch(e.lastUsed=i,this.energy-=e.energy,t){case"rapidFire":return this.activateRapidFire();case"laserBeam":return this.activateLaserBeam();case"missile":return this.activateMissile();case"shield":return this.activateShield();default:return!1}}activateRapidFire(){return this.weaponType="rapid",setTimeout((()=>{this.weaponType="normal"}),3e3),!0}activateLaserBeam(){for(let t=0;t<5;t++)setTimeout((()=>{const t=new z(new M(this.position.x,this.position.y,this.position.z),new M(0,0,-80),!0);t.damage=50,this.projectiles.push(t)}),100*t);return!0}activateMissile(){const t=new z(new M(this.position.x,this.position.y,this.position.z),new M(0,0,-60),!0);return t.damage=100,this.projectiles.push(t),!0}activateShield(){return this.health=Math.min(this.maxHealth,this.health+50),!0}getSkillCooldown(t){const e=this.skills[t];if(!e)return 0;const i=Date.now()-e.lastUsed;return Math.max(0,e.cooldown-i)}canUseSkill(t){const e=this.skills[t];return!!e&&(0===this.getSkillCooldown(t)&&this.energy>=e.energy)}setAudioManager(t){this.audioManager=t}}class j{constructor(t,i="straight"){e(this,"mesh"),e(this,"position"),e(this,"velocity"),e(this,"health"),e(this,"maxHealth"),e(this,"speed"),e(this,"projectiles"),e(this,"boundingBox"),e(this,"lastShotTime"),e(this,"shotCooldown"),e(this,"movementPattern"),e(this,"movementTime"),this.position=t.clone(),this.velocity=new M(0,0,2),this.health=50,this.maxHealth=50,this.speed=3,this.projectiles=[],this.boundingBox=new y,this.lastShotTime=0,this.shotCooldown=1e3,this.movementPattern=i,this.movementTime=0,this.createMesh()}createMesh(){this.mesh=new v;const t=new x(1.2,.8,8,8),e=new S({color:15022389,shininess:80,specular:16733986}),i=new w(t,e);i.rotation.x=-Math.PI/2,i.castShadow=!0,i.receiveShadow=!0,this.mesh.add(i);const s=new E(2.5,1,6),o=new S({color:12986408,shininess:60}),n=new w(s,o);n.position.set(0,.3,0),n.castShadow=!0,this.mesh.add(n);const a=new E(8,.4,2.5),r=new S({color:12000284,shininess:70}),h=new w(a,r);h.position.set(0,0,1),h.castShadow=!0,this.mesh.add(h);const c=new E(.8,.8,1.5),l=new S({color:4342338}),m=new w(c,l);m.position.set(-3,-.2,-1),this.mesh.add(m);const d=new w(c,l);d.position.set(3,-.2,-1),this.mesh.add(d);const p=new x(.5,.7,1.5,6),u=new S({color:1713022,shininess:100}),y=new w(p,u);y.position.set(-2,0,3.5),y.rotation.x=Math.PI/2,y.castShadow=!0,this.mesh.add(y);const M=new w(p,u);M.position.set(2,0,3.5),M.rotation.x=Math.PI/2,M.castShadow=!0,this.mesh.add(M),this.createEnemyFlames();const b=new g(.3,8,8),k=new f({color:16711680}),C=new w(b,k);C.position.set(0,.8,-2),C.name="threatLight",this.mesh.add(C),this.createHealthBar(),this.mesh.position.copy(this.position)}createEnemyFlames(){const t=new b(.3,2,6),e=new f({color:16720384,transparent:!0,opacity:.7}),i=new w(t,e);i.position.set(-2,0,5.5),i.rotation.x=Math.PI/2,i.name="leftFlame",this.mesh.add(i);const s=new w(t,e);s.position.set(2,0,5.5),s.rotation.x=Math.PI/2,s.name="rightFlame",this.mesh.add(s)}createHealthBar(){const t=new k(3,.3),e=new f({color:3355443,transparent:!0,opacity:.8}),i=new w(t,e);i.position.set(0,2,0),i.name="healthBarBg",this.mesh.add(i);const s=new k(3,.25),o=new f({color:65280,transparent:!0,opacity:.9}),n=new w(s,o);n.position.set(0,2,.01),n.name="healthBar",this.mesh.add(n)}update(t,e){this.movementTime+=t,this.updateMovement(t,e),this.position.add(this.velocity.clone().multiplyScalar(t)),this.mesh.position.copy(this.position),this.boundingBox.setFromObject(this.mesh),this.projectiles.forEach(((e,i)=>{e.update(t),e.position.z>200&&this.projectiles.splice(i,1)})),this.updateShooting(e),this.updateAnimations(t),this.updateHealthBar()}updateAnimations(t){this.mesh.rotation.z=.08*Math.sin(2*this.movementTime),this.mesh.rotation.x=.05*Math.sin(1.5*this.movementTime);const e=this.mesh.getObjectByName("leftFlame"),i=this.mesh.getObjectByName("rightFlame");e&&i&&(e.scale.y=1+.3*Math.sin(8*this.movementTime),i.scale.y=1+.3*Math.sin(8*this.movementTime+1));const s=this.mesh.getObjectByName("threatLight");if(s){const t=s.material,e=.5+.3*Math.sin(6*this.movementTime);t.opacity=e}}updateHealthBar(){const t=this.mesh.getObjectByName("healthBar");if(t){const e=this.health/this.maxHealth;t.scale.x=e;const i=t.material;e>.6?i.color.setHex(65280):e>.3?i.color.setHex(16776960):i.color.setHex(16711680);const s=this.mesh.getObjectByName("healthBarBg");s&&(t.lookAt(0,0,0),s.lookAt(0,0,0))}}updateMovement(t,e){switch(this.movementPattern){case"straight":this.velocity.z=this.speed;break;case"zigzag":this.velocity.z=this.speed,this.velocity.x=Math.sin(3*this.movementTime)*this.speed*.5;break;case"circle":this.velocity.x=Math.cos(this.movementTime)*this.speed*.3,this.velocity.z=Math.sin(this.movementTime)*this.speed*.3+.5*this.speed}Math.abs(this.position.x)>80&&(this.velocity.x*=-.5)}updateShooting(t){if(Date.now()-this.lastShotTime<this.shotCooldown)return;const e=this.position.distanceTo(t);if(e<150&&e>20){const e=t.clone().sub(this.position).normalize();e.x+=.3*(Math.random()-.5),e.y+=.3*(Math.random()-.5),e.normalize(),this.shoot(e)}}shoot(t){const e=Date.now();if(e-this.lastShotTime<this.shotCooldown)return!1;this.lastShotTime=e;const i=t||new M(0,0,1);i.multiplyScalar(30);const s=new z(new M(this.position.x,this.position.y,this.position.z-2),i,!1);return this.projectiles.push(s),!0}takeDamage(t,e="normal"){this.health=Math.max(0,this.health-t),this.health>0?this.mesh.children.forEach((t=>{if(t instanceof w){const e=t.material,i=e.color.clone();e.color.setHex(16777215),setTimeout((()=>{e.color.copy(i)}),100)}})):this.createDestroyEffect(e)}createDestroyEffect(t){switch(t){case"missile":this.createMissileDestroyEffect();break;case"laser":this.createLaserDestroyEffect();break;default:this.createNormalDestroyEffect()}}createMissileDestroyEffect(){const t=()=>{this.mesh.rotation.x+=.3,this.mesh.rotation.y+=.2,this.mesh.rotation.z+=.4,this.mesh.scale.multiplyScalar(.95),this.createSparkEffect(),this.mesh.scale.x>.1&&requestAnimationFrame(t)};t()}createLaserDestroyEffect(){this.mesh.children.forEach(((t,e)=>{if(t instanceof w){const i=new M(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize(),s=()=>{t.position.add(i.clone().multiplyScalar(.5)),t.rotation.x+=.1,t.rotation.y+=.1,t.scale.multiplyScalar(.98),t.scale.x>.1&&requestAnimationFrame(s)};setTimeout((()=>s()),50*e)}}))}createNormalDestroyEffect(){const t=()=>{this.mesh.children.forEach((t=>{if(t instanceof w){const e=t.material;e.transparent=!0,e.opacity=Math.max(0,e.opacity-.05)}})),this.mesh.scale.multiplyScalar(1.02),this.mesh.scale.x<1.5&&requestAnimationFrame(t)};t()}createSparkEffect(){for(let t=0;t<8;t++){const e=new w(new g(.1,4,4),new f({color:16755200,transparent:!0,opacity:1}));e.position.copy(this.position),e.position.add(new M(4*(Math.random()-.5),4*(Math.random()-.5),4*(Math.random()-.5)));const i=()=>{e.position.y-=.1;const t=e.material;t.opacity-=.02,t.opacity>0?requestAnimationFrame(i):e.userData.shouldRemove=!0};e.userData.isSpark=!0,this.mesh.add(e),setTimeout((()=>i()),100*t)}}isAlive(){return this.health>0}isOutOfBounds(){return this.position.z>200||Math.abs(this.position.x)>200||Math.abs(this.position.y)>100}getProjectiles(){return this.projectiles}removeProjectile(t){const e=this.projectiles.indexOf(t);e>-1&&this.projectiles.splice(e,1)}getBoundingBox(){return this.boundingBox}getScore(){switch(this.movementPattern){case"straight":default:return 100;case"zigzag":return 150;case"circle":return 200}}}class G{constructor(t,i,s){e(this,"crosshairElement"),e(this,"targetIndicators",[]),e(this,"camera"),e(this,"raycaster"),e(this,"mouse"),e(this,"canvas"),e(this,"isAiming",!1),e(this,"zoomLevel",1),e(this,"maxZoom",3),e(this,"minZoom",1),this.camera=t,this.canvas=s,this.raycaster=new C,this.mouse=new T,this.createCrosshair(),this.initEventListeners()}createCrosshair(){this.crosshairElement=document.createElement("div"),this.crosshairElement.id="crosshair",this.crosshairElement.innerHTML='\n      <div class="crosshair-center"></div>\n      <div class="crosshair-line crosshair-top"></div>\n      <div class="crosshair-line crosshair-bottom"></div>\n      <div class="crosshair-line crosshair-left"></div>\n      <div class="crosshair-line crosshair-right"></div>\n      <div class="crosshair-circle"></div>\n      <div class="zoom-indicator">\n        <span class="zoom-text">1.0x</span>\n      </div>\n    ',document.body.appendChild(this.crosshairElement),this.addCrosshairStyles()}addCrosshairStyles(){const t=document.createElement("style");t.textContent="\n      #crosshair {\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        pointer-events: none;\n        z-index: 1000;\n        transition: all 0.3s ease;\n      }\n\n      .crosshair-center {\n        width: 4px;\n        height: 4px;\n        background: #00ff00;\n        border-radius: 50%;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        box-shadow: 0 0 10px #00ff00;\n      }\n\n      .crosshair-line {\n        background: #00ff00;\n        position: absolute;\n        box-shadow: 0 0 5px #00ff00;\n      }\n\n      .crosshair-top, .crosshair-bottom {\n        width: 2px;\n        height: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      .crosshair-top {\n        top: -30px;\n      }\n\n      .crosshair-bottom {\n        bottom: -30px;\n      }\n\n      .crosshair-left, .crosshair-right {\n        width: 20px;\n        height: 2px;\n        top: 50%;\n        transform: translateY(-50%);\n      }\n\n      .crosshair-left {\n        left: -30px;\n      }\n\n      .crosshair-right {\n        right: -30px;\n      }\n\n      .crosshair-circle {\n        width: 80px;\n        height: 80px;\n        border: 1px solid #00ff00;\n        border-radius: 50%;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        opacity: 0.6;\n        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);\n      }\n\n      .zoom-indicator {\n        position: absolute;\n        top: -60px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        padding: 5px 10px;\n        border-radius: 5px;\n        border: 1px solid #00ff00;\n      }\n\n      .zoom-text {\n        color: #00ff00;\n        font-size: 14px;\n        font-weight: bold;\n        text-shadow: 0 0 5px #00ff00;\n      }\n\n      #crosshair.aiming {\n        transform: translate(-50%, -50%) scale(1.2);\n      }\n\n      #crosshair.aiming .crosshair-center {\n        background: #ff4400;\n        box-shadow: 0 0 15px #ff4400;\n      }\n\n      #crosshair.aiming .crosshair-line {\n        background: #ff4400;\n        box-shadow: 0 0 8px #ff4400;\n      }\n\n      #crosshair.aiming .crosshair-circle {\n        border-color: #ff4400;\n        box-shadow: 0 0 20px rgba(255, 68, 0, 0.5);\n      }\n\n      .target-indicator {\n        position: fixed;\n        width: 30px;\n        height: 30px;\n        border: 2px solid #ff0000;\n        border-radius: 50%;\n        pointer-events: none;\n        z-index: 999;\n        animation: targetPulse 1s infinite;\n        transform: translate(-50%, -50%);\n      }\n\n      @keyframes targetPulse {\n        0%, 100% { \n          transform: translate(-50%, -50%) scale(1);\n          opacity: 1;\n        }\n        50% { \n          transform: translate(-50%, -50%) scale(1.2);\n          opacity: 0.7;\n        }\n      }\n\n      .distance-indicator {\n        position: absolute;\n        top: -25px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: #ff0000;\n        padding: 2px 6px;\n        border-radius: 3px;\n        font-size: 10px;\n        font-weight: bold;\n        white-space: nowrap;\n      }\n    ",document.head.appendChild(t)}initEventListeners(){this.canvas.addEventListener("mousemove",(t=>{this.updateMousePosition(t)})),this.canvas.addEventListener("mousedown",(t=>{2===t.button&&this.startAiming()})),this.canvas.addEventListener("mouseup",(t=>{2===t.button&&this.stopAiming()})),this.canvas.addEventListener("wheel",(t=>{t.preventDefault(),this.handleZoom(t.deltaY)})),this.canvas.addEventListener("contextmenu",(t=>{t.preventDefault()}))}updateMousePosition(t){const e=this.canvas.getBoundingClientRect();this.mouse.x=(t.clientX-e.left)/e.width*2-1,this.mouse.y=-(t.clientY-e.top)/e.height*2+1}startAiming(){this.isAiming=!0,this.crosshairElement.classList.add("aiming"),this.camera.fov=75/this.zoomLevel,this.camera.updateProjectionMatrix()}stopAiming(){this.isAiming=!1,this.crosshairElement.classList.remove("aiming"),this.camera.fov=75,this.camera.updateProjectionMatrix()}handleZoom(t){if(!this.isAiming)return;this.zoomLevel=t>0?Math.max(this.minZoom,this.zoomLevel-.1):Math.min(this.maxZoom,this.zoomLevel+.1),this.camera.fov=75/this.zoomLevel,this.camera.updateProjectionMatrix();const e=this.crosshairElement.querySelector(".zoom-text");e&&(e.textContent=`${this.zoomLevel.toFixed(1)}x`)}update(t,e){if(this.clearTargetIndicators(),this.updateCrosshairAnimation(),!this.isAiming)return void this.showNearbyEnemies(t,e);this.raycaster.setFromCamera(this.mouse,this.camera);let i=null,s=1/0;t.forEach((t=>{const e=this.raycaster.intersectObject(t.mesh,!0);if(e.length>0){const o=e[0].distance;o<s&&(s=o,i=t),this.createTargetIndicator(t,o,o===s)}})),this.updateAimingAccuracy(i,s)}createTargetIndicator(t,e,i=!1){const s=t.position.clone();s.project(this.camera);const o=(.5*s.x+.5)*window.innerWidth,n=(-.5*s.y+.5)*window.innerHeight,a=document.createElement("div");a.className=i?"target-indicator primary-target":"target-indicator",a.style.left=`${o}px`,a.style.top=`${n}px`,i&&(a.style.borderColor="#ff0000",a.style.borderWidth="3px",a.style.boxShadow="0 0 20px rgba(255, 0, 0, 0.8)");const r=document.createElement("div");r.className="target-info",r.innerHTML=`\n      <div class="distance">${Math.round(e)}m</div>\n      <div class="health">${Math.round(t.health)}HP</div>\n    `,r.style.cssText=`\n      position: absolute;\n      top: -40px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: ${i?"#ff0000":"#ffffff"};\n      padding: 2px 6px;\n      border-radius: 3px;\n      font-size: 10px;\n      font-weight: bold;\n      white-space: nowrap;\n      text-align: center;\n    `,a.appendChild(r),document.body.appendChild(a),this.targetIndicators.push(a)}clearTargetIndicators(){this.targetIndicators.forEach((t=>{t.parentNode&&t.parentNode.removeChild(t)})),this.targetIndicators=[]}getAimingDirection(){return this.raycaster.setFromCamera(this.mouse,this.camera),this.raycaster.ray.direction.clone()}isCurrentlyAiming(){return this.isAiming}getZoomLevel(){return this.zoomLevel}updateCrosshairAnimation(){const t=.001*Date.now(),e=1+.02*Math.sin(2*t);this.crosshairElement.style.transform=`translate(-50%, -50%) scale(${e})`;const i=this.crosshairElement.querySelector(".crosshair-center");i&&(i.style.opacity=(.8+.2*Math.sin(4*t)).toString())}showNearbyEnemies(t,e){e&&t.forEach((t=>{const i=t.position.distanceTo(e);i<200&&this.createDirectionIndicator(t,i)}))}createDirectionIndicator(t,e){const i=t.position.clone();i.project(this.camera);let s=(.5*i.x+.5)*window.innerWidth,o=(-.5*i.y+.5)*window.innerHeight;s=Math.max(50,Math.min(window.innerWidth-50,s)),o=Math.max(50,Math.min(window.innerHeight-50,o));const n=document.createElement("div");n.className="direction-indicator",n.style.cssText=`\n      position: fixed;\n      left: ${s}px;\n      top: ${o}px;\n      width: 20px;\n      height: 20px;\n      border: 2px solid #ffaa00;\n      border-radius: 50%;\n      transform: translate(-50%, -50%);\n      pointer-events: none;\n      z-index: 998;\n      opacity: ${Math.max(.3,1-e/200)};\n    `,document.body.appendChild(n),this.targetIndicators.push(n)}updateAimingAccuracy(t,e){if(!this.crosshairElement.querySelector(".accuracy-indicator")){const t=document.createElement("div");t.className="accuracy-indicator",t.style.cssText="\n        position: absolute;\n        bottom: -80px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        padding: 5px 10px;\n        border-radius: 5px;\n        border: 1px solid #00ff00;\n        color: #00ff00;\n        font-size: 12px;\n        font-weight: bold;\n        text-align: center;\n        min-width: 100px;\n      ",this.crosshairElement.appendChild(t)}const i=this.crosshairElement.querySelector(".accuracy-indicator");if(t&&e<300){const t=Math.max(20,100-e/3);i.textContent=`命中率: ${Math.round(t)}%`,i.style.color=t>70?"#00ff00":t>40?"#ffff00":"#ff4400",i.style.borderColor=i.style.color}else i.textContent="无目标",i.style.color="#666666",i.style.borderColor="#666666"}dispose(){this.clearTargetIndicators(),this.crosshairElement.parentNode&&this.crosshairElement.parentNode.removeChild(this.crosshairElement)}}class I{constructor(t,i,s){e(this,"keys",{}),e(this,"mouse",{x:0,y:0}),e(this,"player"),e(this,"camera"),e(this,"canvas"),e(this,"crosshair",null),e(this,"isMouseLocked",!1),e(this,"audioManager",null),this.player=t,this.camera=i,this.canvas=s,this.initEventListeners()}setCrosshair(t){this.crosshair=t}setAudioManager(t){this.audioManager=t}initEventListeners(){document.addEventListener("keydown",(t=>this.onKeyDown(t))),document.addEventListener("keyup",(t=>this.onKeyUp(t))),this.canvas.addEventListener("click",(()=>this.requestPointerLock())),document.addEventListener("pointerlockchange",(()=>this.onPointerLockChange())),document.addEventListener("mousemove",(t=>this.onMouseMove(t))),this.canvas.addEventListener("contextmenu",(t=>t.preventDefault()))}onKeyDown(t){if(this.keys[t.code.toLowerCase()]=!0,"KeyM"===t.code&&this.audioManager)return this.audioManager.toggleMute().then((t=>{console.log(t?"🔇 音乐已静音":"🎵 音乐已开启")})),void t.preventDefault();["Space","ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(t.code)&&t.preventDefault()}onKeyUp(t){this.keys[t.code.toLowerCase()]=!1}onMouseMove(t){if(!this.isMouseLocked)return;this.mouse.x+=.002*t.movementX,this.mouse.y+=.002*t.movementY,this.mouse.x=Math.max(-Math.PI/4,Math.min(Math.PI/4,this.mouse.x)),this.mouse.y=Math.max(-Math.PI/6,Math.min(Math.PI/6,this.mouse.y))}requestPointerLock(){this.canvas.requestPointerLock()}onPointerLockChange(){this.isMouseLocked=document.pointerLockElement===this.canvas}update(){this.handleMovement(),this.handleShooting(),this.handleCamera()}handleMovement(){(this.keys.keyw||this.keys.arrowup)&&this.player.moveForward(),(this.keys.keys||this.keys.arrowdown)&&this.player.moveBackward(),(this.keys.keya||this.keys.arrowleft)&&this.player.moveLeft(),(this.keys.keyd||this.keys.arrowright)&&this.player.moveRight(),this.keys.keyq&&this.player.moveUp(),this.keys.keye&&this.player.moveDown()}handleShooting(){if(this.keys.space){let t;this.crosshair&&this.crosshair.isCurrentlyAiming()&&(t=this.crosshair.getAimingDirection()),this.player.shoot(t)}this.keys.digit1&&this.player.useSkill("rapidFire"),this.keys.digit2&&this.player.useSkill("laserBeam"),this.keys.digit3&&this.player.useSkill("missile"),this.keys.digit4&&this.player.useSkill("shield")}handleCamera(){const t=this.player.position,e=new M(0,20,50);this.isMouseLocked&&(e.x+=30*this.mouse.x,e.y+=20*this.mouse.y);const i=t.clone().add(e);this.camera.position.lerp(i,.1);const s=t.clone().add(new M(10*this.mouse.x,5*this.mouse.y,-20));this.camera.lookAt(s)}getControlsInfo(){return["WASD / 方向键 - 移动飞机","Q/E - 上升/下降","空格键 - 发射炮弹","右键 - 瞄准模式","滚轮 - 缩放瞄准镜","1 - 快速射击技能","2 - 激光束技能","3 - 导弹技能","4 - 护盾技能","鼠标 - 控制视角","点击画面锁定鼠标"]}isKeyPressed(t){return this.keys[t.toLowerCase()]||!1}getMousePosition(){return{...this.mouse}}getMouseLockStatus(){return this.isMouseLocked}exitPointerLock(){this.isMouseLocked&&document.exitPointerLock()}reset(){this.keys={},this.mouse={x:0,y:0},this.exitPointerLock()}dispose(){document.removeEventListener("keydown",this.onKeyDown),document.removeEventListener("keyup",this.onKeyUp),document.removeEventListener("pointerlockchange",this.onPointerLockChange),document.removeEventListener("mousemove",this.onMouseMove),this.canvas.removeEventListener("click",this.requestPointerLock),this.canvas.removeEventListener("contextmenu",(t=>t.preventDefault()))}}new class{constructor(){e(this,"gameWorld"),e(this,"player"),e(this,"enemies",[]),e(this,"controls"),e(this,"crosshair"),e(this,"audioManager"),e(this,"gameRunning",!1),e(this,"score",0),e(this,"enemySpawnTimer",0),e(this,"enemySpawnInterval",2e3),e(this,"lastTime",0),e(this,"gameStartTime",0),e(this,"scoreElement"),e(this,"healthElement"),e(this,"energyElement"),e(this,"skillElements",{}),this.initGame()}initGame(){const t=document.querySelector("#game-canvas");if(!t)throw new Error("无法找到游戏画布元素 #game-canvas");this.gameWorld=new P(t),this.player=new D,this.gameWorld.addToScene(this.player.mesh),this.controls=new I(this.player,this.gameWorld.camera,t),this.crosshair=new G(this.gameWorld.camera,this.gameWorld.scene,t),this.controls.setCrosshair(this.crosshair),this.audioManager=new F,this.controls.setAudioManager(this.audioManager),this.player.setAudioManager(this.audioManager),this.scoreElement=document.querySelector("#score"),this.healthElement=document.querySelector("#health"),this.energyElement=document.querySelector("#energy"),this.skillElements={rapidFire:document.querySelector("#skill-1"),laserBeam:document.querySelector("#skill-2"),missile:document.querySelector("#skill-3"),shield:document.querySelector("#skill-4")},window.addEventListener("resize",(()=>this.gameWorld.onWindowResize())),this.startGame()}async startGame(){this.gameRunning=!0,this.gameStartTime=Date.now(),this.lastTime=performance.now();try{await this.audioManager.playBGM(),console.log("🎵 太空战斗BGM开始播放")}catch(t){console.warn("BGM播放失败，可能需要用户交互:",t)}this.gameLoop(),console.log("🚀 太空战斗游戏已启动！"),console.log("🎮 控制说明："),console.log("  WASD - 移动飞机"),console.log("  Q/E - 上升/下降"),console.log("  空格键 - 发射炮弹"),console.log("  鼠标 - 控制视角（点击画面锁定鼠标）"),console.log("  M键 - 切换音乐开关")}gameLoop(){if(!this.gameRunning)return;const t=performance.now(),e=(t-this.lastTime)/1e3;this.lastTime=t,this.controls.update(),this.player.update(e),this.updateEnemies(e),this.crosshair.update(this.enemies,this.player.position),this.spawnEnemies(),this.checkCollisions(),this.cleanup(),this.updateUI(),this.gameWorld.render(),requestAnimationFrame((()=>this.gameLoop()))}updateEnemies(t){for(let e=this.enemies.length-1;e>=0;e--){const i=this.enemies[e];i.update(t,this.player.position),i.isAlive()&&!i.isOutOfBounds()||(this.gameWorld.removeFromScene(i.mesh),i.isAlive()||(this.score+=i.getScore(),console.log(`敌机被击毁！得分 +${i.getScore()}，总分: ${this.score}`)),this.enemies.splice(e,1))}}spawnEnemies(){const t=Date.now();if(t-this.enemySpawnTimer>this.enemySpawnInterval){this.enemySpawnTimer=t;const e=150*(Math.random()-.5),i=50*(Math.random()-.5),s=-200,o=["straight","zigzag","circle"],n=o[Math.floor(Math.random()*o.length)],a=new j(new M(e,i,s),n);this.enemies.push(a),this.gameWorld.addToScene(a.mesh);const r=(t-this.gameStartTime)/1e3;this.enemySpawnInterval=Math.max(500,2e3-10*r)}}checkCollisions(){for(let t=this.player.getProjectiles().length-1;t>=0;t--){const e=this.player.getProjectiles()[t];for(let t=this.enemies.length-1;t>=0;t--){const i=this.enemies[t];if(e.checkCollision(i.mesh)){const t=this.getWeaponTypeFromProjectile(e);i.takeDamage(e.damage,t),this.audioManager.playSoundEffect("hit"),this.gameWorld.removeFromScene(e.mesh),this.player.removeProjectile(e),"missile"===t?(this.createExplosion(e.position,!0),this.audioManager.playSoundEffect("explosion")):this.createExplosion(e.position),i.isAlive()||(this.createEnemyDestroyEffect(i.position,t),this.audioManager.playSoundEffect("explosion"));break}}}this.enemies.forEach((t=>{for(let e=t.getProjectiles().length-1;e>=0;e--){const i=t.getProjectiles()[e];i.checkCollision(this.player.mesh)&&(this.player.takeDamage(i.damage),this.audioManager.playSoundEffect("hit"),this.gameWorld.removeFromScene(i.mesh),t.removeProjectile(i),this.createExplosion(i.position))}})),this.enemies.forEach((t=>{t.position.distanceTo(this.player.position)<8&&(this.player.takeDamage(50),t.takeDamage(100),this.audioManager.playSoundEffect("explosion"),this.createExplosion(t.position,!0),this.createScreenShake())})),this.player.isAlive()||this.gameOver()}createScreenShake(){const t=this.gameWorld.camera,e=t.position.clone();let i=0;const s=()=>{if(i<.5){const o=2*(Math.random()-.5),n=2*(Math.random()-.5);t.position.x=e.x+o,t.position.y=e.y+n,i+=.016,requestAnimationFrame(s)}else t.position.copy(e)};s()}cleanup(){this.player.getProjectiles().forEach((t=>{t.isOutOfBounds()?(this.gameWorld.removeFromScene(t.mesh),this.player.removeProjectile(t)):this.gameWorld.scene.children.includes(t.mesh)||this.gameWorld.addToScene(t.mesh)})),this.enemies.forEach((t=>{t.getProjectiles().forEach((e=>{e.isOutOfBounds()?(this.gameWorld.removeFromScene(e.mesh),t.removeProjectile(e)):this.gameWorld.scene.children.includes(e.mesh)||this.gameWorld.addToScene(e.mesh)}))}))}updateUI(){this.scoreElement.textContent=`得分: ${this.score}`,this.healthElement.textContent=`生命值: ${Math.round(this.player.health)}`,this.energyElement.textContent=`能量: ${Math.round(this.player.energy)}`,this.player.health>60?this.healthElement.style.color="#00ff00":this.player.health>30?this.healthElement.style.color="#ffff00":this.healthElement.style.color="#ff0000",this.updateSkillsUI()}updateSkillsUI(){["rapidFire","laserBeam","missile","shield"].forEach((t=>{const e=this.skillElements[t];if(!e)return;const i=this.player.canUseSkill(t),s=this.player.getSkillCooldown(t),o=this.player.skills[t];e.className="skill-slot",i?e.classList.add("available"):s>0&&e.classList.add("cooldown");const n=e.querySelector(".skill-cooldown");if(n&&o){const t=s/o.cooldown;n.style.transform=`scaleX(${t})`,n.style.display=s>0?"block":"none"}}))}createExplosion(t,e=!1){const i=e?20:10,s=[];for(let n=0;n<i;n++){const i=new g(e?.5:.2,4,4),o=new f({color:e?16729088:16755200,transparent:!0,opacity:.8}),n=new w(i,o);n.position.copy(t);const a=new M(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize(),r=Math.random()*(e?20:10)+5;n.userData={velocity:a.multiplyScalar(r),life:1,maxLife:e?2:1},s.push(n),this.gameWorld.addToScene(n)}const o=()=>{s.forEach(((t,e)=>{if(t.userData.life<=0)return this.gameWorld.removeFromScene(t),void s.splice(e,1);t.position.add(t.userData.velocity.clone().multiplyScalar(.016)),t.userData.life-=.016;t.material.opacity=t.userData.life/t.userData.maxLife,t.userData.velocity.multiplyScalar(.98)})),s.length>0&&requestAnimationFrame(o)};o()}gameOver(){this.gameRunning=!1,this.audioManager.stopBGM();const t=document.createElement("div");t.style.cssText="\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 30px;\n      border-radius: 10px;\n      text-align: center;\n      font-size: 24px;\n      z-index: 1000;\n    ",t.innerHTML=`\n      <h2 style="color: #ff4444; margin-bottom: 20px;">游戏结束</h2>\n      <p>最终得分: ${this.score}</p>\n      <button onclick="location.reload()" style="\n        margin-top: 20px;\n        padding: 10px 20px;\n        font-size: 18px;\n        background: #0088ff;\n        color: white;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n      ">重新开始</button>\n    `,document.body.appendChild(t),console.log(`游戏结束！最终得分: ${this.score}`)}getWeaponTypeFromProjectile(t){return t.damage>=80?"missile":t.damage>=40?"laser":"normal"}createEnemyDestroyEffect(t,e){switch(e){case"missile":this.createMissileDestroyEffect(t);break;case"laser":this.createLaserDestroyEffect(t);break;default:this.createNormalDestroyEffect(t)}}createMissileDestroyEffect(t){const e=new A(0,1,16),i=new f({color:16729088,transparent:!0,opacity:.8,side:L}),s=new w(e,i);s.position.copy(t),this.gameWorld.addToScene(s);const o=()=>{s.scale.multiplyScalar(1.1),i.opacity-=.02,i.opacity>0?requestAnimationFrame(o):this.gameWorld.removeFromScene(s)};o(),this.createFireball(t)}createLaserDestroyEffect(t){const e=new w(new g(2,8,8),new f({color:65535,transparent:!0,opacity:.8}));e.position.copy(t),this.gameWorld.addToScene(e);const i=()=>{e.scale.multiplyScalar(1.05);const t=e.material;t.opacity-=.03,t.opacity>0?requestAnimationFrame(i):this.gameWorld.removeFromScene(e)};i()}createNormalDestroyEffect(t){for(let e=0;e<6;e++){const i=new w(new E(.3,.3,.3),new f({color:6710886,transparent:!0,opacity:1}));i.position.copy(t);const s=new M(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize();this.gameWorld.addToScene(i);const o=()=>{i.position.add(s.clone().multiplyScalar(.3)),i.rotation.x+=.1,i.rotation.y+=.1;const t=i.material;t.opacity-=.02,t.opacity>0?requestAnimationFrame(o):this.gameWorld.removeFromScene(i)};setTimeout((()=>o()),50*e)}}createFireball(t){const e=new w(new g(3,12,12),new f({color:16737792,transparent:!0,opacity:.9}));e.position.copy(t),this.gameWorld.addToScene(e);const i=()=>{e.scale.multiplyScalar(1.03);const t=e.material;t.opacity-=.025,t.opacity>.5?t.color.setHex(16737792):t.opacity>.2?t.color.setHex(16711680):t.color.setHex(3342336),t.opacity>0?requestAnimationFrame(i):this.gameWorld.removeFromScene(e)};i()}};
