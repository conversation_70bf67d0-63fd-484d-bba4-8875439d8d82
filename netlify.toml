[build]
  # 构建命令
  command = "npm run build"
  
  # 构建输出目录
  publish = "dist"
  
  # Node.js版本
  environment = { NODE_VERSION = "18" }

[build.environment]
  # 环境变量
  NODE_ENV = "production"

# 重定向规则 - SPA路由支持
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 头部设置 - 缓存优化
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# 安全头部
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
